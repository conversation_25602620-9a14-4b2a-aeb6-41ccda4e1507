// lib/components/speed_indicator.dart

import 'package:flutter/material.dart';

class SpeedIndicator extends StatelessWidget {
  final double speed;
  final String speedUnit;
  final String label;
  final IconData icon;

  const SpeedIndicator({
    Key? key,
    required this.speed,
    this.speedUnit = 'km/h',
    this.label = 'Speed',
    this.icon = Icons.speed,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // Determine text color based on theme brightness
    final bool isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final Color textColor = isDarkMode ? Colors.white : Colors.black;

    return Padding(
      padding: const EdgeInsetsDirectional.fromSTEB(0, 0, 0, 12),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: _getSpeedColor(),
            size: 18,
          ),
          const SizedBox(width: 2), // Match weather display spacing
          Flexible(
            child: TweenAnimationBuilder<double>(
              tween: Tween<double>(
                begin: speed - 1,
                end: speed,
              ),
              duration: const Duration(seconds: 2),
              builder: (context, value, child) {
                return Text(
                  '${value.toStringAsFixed(0)}km/h',
                  overflow: TextOverflow.ellipsis,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontFamily: 'PorscheNumber',
                        fontSize: 18, // Keep consistent with other indicators
                        color: textColor,
                      ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Color _getSpeedColor() {
    if (speed == 0) {
      return Colors.grey;
    } else if (speed > 0 && speed <= 50) {
      return Colors.green; // Safe speed range
    } else if (speed > 50 && speed <= 80) {
      return Colors.orange; // Moderate speed range
    } else {
      return Colors.red; // High speed range
    }
  }
}
